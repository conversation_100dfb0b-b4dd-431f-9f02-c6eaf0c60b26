# Headers for WebAssembly multithreading support in Chrome
# Required for SharedArrayBuffer and Atomics

/*
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin

/stockfish/*
  Cross-Origin-Resource-Policy: cross-origin
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type

/*.wasm
  Content-Type: application/wasm
  Cross-Origin-Resource-Policy: cross-origin

/*.js
  Cross-Origin-Resource-Policy: cross-origin
