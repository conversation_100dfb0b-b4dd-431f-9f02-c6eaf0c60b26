PODS:
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - stockfish_chess_engine (0.0.1):
    - Flutter
  - Toast (4.1.1)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - stockfish_chess_engine (from `.symlinks/plugins/stockfish_chess_engine/ios`)

SPEC REPOS:
  trunk:
    - Toast

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  stockfish_chess_engine:
    :path: ".symlinks/plugins/stockfish_chess_engine/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  stockfish_chess_engine: a6bc54098dd17dcf19b288084925edf492576045
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e

PODFILE CHECKSUM: c4c93c5f6502fe2754f48404d3594bf779584011

COCOAPODS: 1.16.2
