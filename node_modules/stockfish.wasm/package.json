{"name": "stockfish.wasm", "version": "0.10.0", "description": "WebAssembly port of the strong chess engine Stockfish", "keywords": ["chess", "stockfish", "emscripten", "lichess", "webassembly"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "GPL-3.0", "repository": "github:niklasf/stockfish.wasm", "homepage": "https://github.com/niklasf/stockfish.wasm#readme", "bugs": {"url": "https://github.com/niklasf/stockfish.wasm/issues", "email": "<EMAIL>"}, "main": "stockfish.js", "files": ["Copying.txt", "stockfish.js", "stockfish.wasm", "stockfish.worker.js"], "scripts": {"prepare": "cd src && make clean && make ARCH=wasm build -j && cd .. && cat preamble.js src/stockfish.js > stockfish.js && cp src/stockfish.worker.js src/stockfish.wasm ."}}