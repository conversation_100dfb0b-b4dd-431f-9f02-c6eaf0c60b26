# AI 难度分级策略设计方案

## 项目背景

原有的AI难度分级系统仅基于思考时间进行简单分级，存在以下问题：
1. **设备性能差异**：不同设备的运算资源不同，相同的思考时间在不同设备上效果差异很大
2. **难度层次单一**：只有3个等级（简单、中等、困难），无法满足不同水平玩家的需求
3. **策略维度单一**：仅通过时间控制难度，缺乏其他AI行为调整手段

## 新设计方案

### 1. 智能设备检测

#### 设备分类
- **Web端**：浏览器环境，性能中等，受JavaScript限制
- **桌面端**：Windows/macOS/Linux，性能最强，资源充足
- **移动端**：Android/iOS/HarmonyOS，性能受限，需要节能

#### 自动适配机制
```dart
static DeviceType getCurrentDeviceType() {
  if (kIsWeb) return DeviceType.web;
  if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
    return DeviceType.desktop;
  }
  return DeviceType.mobile; // 包括HarmonyOS
}
```

### 2. 九级精细难度分级

#### 难度等级设计
| 等级 | 名称 | 目标用户 | 主要特征 |
|------|------|----------|----------|
| 1 | 初学者 | 完全新手 | 40%随机性，评估权重0.5，极短思考时间 |
| 2 | 新手 | 刚学会走棋 | 30%随机性，评估权重0.6，基础战术 |
| 3 | 入门 | 休闲玩家 | 20%随机性，评估权重0.7，启用开局库 |
| 4 | 中等 | 有经验玩家 | 10%随机性，评估权重0.8，动态时间管理 |
| 5 | 进阶 | 高级玩家 | 5%随机性，评估权重0.9，启用残局库 |
| 6 | 专家 | 专业棋手 | 2%随机性，评估权重0.95，深度搜索 |
| 7 | 大师 | 资深棋手 | 1%随机性，评估权重0.98，多线程计算 |
| 8 | 超级大师 | 顶级玩家 | 0.5%随机性，评估权重0.99，深度12层 |
| 9 | 引擎级 | 极限挑战 | 无随机性，评估权重1.0，无深度限制 |

### 3. 多维度难度控制策略

#### 3.1 思考时间控制
- **基础时间**：根据难度等级设置基础思考时间
- **设备调整**：
  - 桌面端：时间系数 × 1.2（可给更多思考时间）
  - Web端：时间系数 × 1.0（标准时间）
  - 移动端：时间系数 × 0.6（节能模式）
- **动态时间**：高级难度启用动态时间分配

#### 3.2 随机性策略
- **概率控制**：每个难度等级对应不同的随机决策概率
- **智能随机**：不是完全随机，而是在合法移动中进行智能选择
  - 初级：完全随机
  - 中级：优先考虑吃子移动
  - 高级：选择相对合理的移动

#### 3.3 评估函数调整
- **权重控制**：通过评估权重模拟不同水平的判断能力
- **噪声注入**：低难度添加高斯噪声，模拟人类不完美的判断

#### 3.4 搜索深度限制
- **深度控制**：低难度限制搜索深度，避免计算过于深入
- **无限制模式**：最高难度允许引擎自由搜索

#### 3.5 辅助功能控制
- **开局库**：中级以上启用，提供标准开局知识
- **残局库**：高级以上启用，提供精确残局计算
- **多线程**：根据设备性能和难度等级调整线程数

### 4. 向后兼容性

#### 4.1 旧系统兼容
```dart
// 保留原有的简单枚举
enum AIDifficulty { easy, medium, hard }

// 提供映射扩展
extension AIDifficultyExtension on AIDifficulty {
  AIDifficultyLevel toNewDifficultyLevel() {
    switch (this) {
      case AIDifficulty.easy: return AIDifficultyLevel.novice;
      case AIDifficulty.medium: return AIDifficultyLevel.intermediate;
      case AIDifficulty.hard: return AIDifficultyLevel.expert;
    }
  }
}
```

#### 4.2 构造函数兼容
```dart
// 旧的构造方式
ChessAI(difficulty: AIDifficulty.medium)

// 新的高级构造方式  
ChessAI.advanced(advancedDifficulty: AIDifficultyLevel.expert)
```

### 5. 用户界面设计

#### 5.1 快速选择器
- 显示推荐的3-5个难度等级
- 根据设备类型自动调整推荐列表
- 简洁的图标和颜色编码

#### 5.2 详细选择器
- 完整的9级难度展示
- 每个难度的详细配置信息
- 设备性能状态显示
- 实时预览思考时间等参数

#### 5.3 智能推荐
- 移动端默认推荐较低难度（1-5级）
- 桌面端支持所有难度等级
- 根据历史选择记录个性化推荐

### 6. 技术实现细节

#### 6.1 配置管理
```dart
class AIDifficultyConfig {
  final int thinkingTimeMs;           // 思考时间
  final double randomnessProbability; // 随机性概率
  final int searchDepth;              // 搜索深度  
  final bool useOpeningBook;          // 开局库
  final bool useEndgameTablebase;     // 残局库
  final double evaluationWeight;      // 评估权重
  final bool useDynamicTiming;        // 动态时间
  final int threads;                  // 线程数
}
```

#### 6.2 性能优化
- **配置缓存**：避免重复计算配置参数
- **延迟初始化**：仅在需要时创建AI实例
- **内存管理**：及时释放不用的AI资源

#### 6.3 错误处理
- **参数验证**：确保所有配置参数在有效范围内
- **回退机制**：AI计算失败时的智能回退策略
- **平台兼容**：处理不同平台的API差异

### 7. 测试策略

#### 7.1 单元测试
- 配置生成的正确性测试
- 设备检测的准确性测试
- 随机性概率的统计验证
- 向后兼容性测试

#### 7.2 性能测试
- 配置生成的执行效率
- 内存使用情况监控
- 不同设备的响应时间对比

#### 7.3 集成测试
- UI组件的交互测试
- AI实例的创建和使用
- 游戏流程的完整性验证

### 8. 未来扩展方向

#### 8.1 自适应难度
- 根据玩家表现动态调整AI强度
- 学习玩家的游戏风格和水平
- 提供个性化的挑战体验

#### 8.2 更多参数控制
- 开局偏好设置（进攻型/防守型）
- 战术风格选择（位置型/战术型）
- 时间控制模式（闪电战/长考）

#### 8.3 AI个性化
- 不同的AI"个性"设置
- 模拟真实棋手的风格
- 提供历史名棋手的AI模型

### 9. 部署建议

#### 9.1 渐进式部署
1. **Phase 1**：部署新的难度分级系统，保持UI不变
2. **Phase 2**：启用新的难度选择界面，提供切换选项
3. **Phase 3**：默认使用新系统，旧系统作为备选

#### 9.2 用户教育
- 在设置界面添加难度说明
- 提供游戏内的难度建议提示
- 创建帮助文档解释新功能

#### 9.3 数据收集
- 收集用户的难度选择偏好
- 分析不同设备的性能表现
- 监控AI响应时间的分布情况

## 总结

新的AI难度分级策略通过以下方式解决了原有系统的问题：

1. **智能设备适配**：自动检测设备类型，根据硬件性能调整AI参数
2. **精细难度控制**：从3级扩展到9级，满足不同水平玩家需求  
3. **多维度策略**：不仅控制时间，还调整随机性、搜索深度、辅助功能等
4. **良好的用户体验**：提供直观的选择界面和详细的参数说明
5. **向后兼容**：保持API兼容性，支持渐进式升级

这个设计既解决了技术问题，又提升了用户体验，为不同水平的玩家提供了合适的挑战。